
                        <!DOCTYPE html>
                        <html lang="en">
                        <head>
                            <meta charset="UTF-8">
                            <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <style>
                body {
                  background-color: white; /* Ensure the iframe has a white background */
                }
    
                
              </style>
                        </head>
                        <body>
                            <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Neon Glow Todo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            color: #fff;
        }

        .container {
            width: 100%;
            max-width: 500px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
            padding: 30px;
            position: relative;
        }

        .container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(
                transparent, 
                rgba(168, 239, 255, 0.5), 
                transparent 30%
            );
            animation: rotate 6s linear infinite;
            z-index: -1;
        }

        @keyframes rotate {
            100% {
                transform: rotate(360deg);
            }
        }

        header {
            text-align: center;
            margin-bottom: 30px;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff7e5f, #feb47b);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 0 0 10px rgba(255, 126, 95, 0.3);
        }

        .subtitle {
            color: #a0a0c0;
            font-size: 1rem;
        }

        .input-section {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
        }

        #task-input {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 50px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        #task-input:focus {
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 15px rgba(168, 239, 255, 0.3);
        }

        #task-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        #add-btn {
            background: linear-gradient(45deg, #ff7e5f, #feb47b);
            border: none;
            border-radius: 50px;
            color: white;
            padding: 0 25px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(255, 126, 95, 0.4);
        }

        #add-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(255, 126, 95, 0.6);
        }

        #add-btn:active {
            transform: translateY(0);
        }

        .filters {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50px;
            padding: 5px;
        }

        .filter-btn {
            flex: 1;
            background: transparent;
            border: none;
            color: #a0a0c0;
            padding: 10px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn.active, .filter-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            color: #a0a0c0;
            font-size: 0.9rem;
        }

        #task-list {
            list-style: none;
            max-height: 400px;
            overflow-y: auto;
            padding-right: 5px;
        }

        #task-list::-webkit-scrollbar {
            width: 8px;
        }

        #task-list::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        #task-list::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #ff7e5f, #feb47b);
            border-radius: 10px;
        }

        .task-item {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            animation: fadeIn 0.3s ease;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .task-item:hover {
            background: rgba(255, 255, 255, 0.12);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .task-checkbox {
            margin-right: 15px;
            width: 22px;
            height: 22px;
            accent-color: #ff7e5f;
            cursor: pointer;
        }

        .task-text {
            flex: 1;
            font-size: 1.1rem;
            word-break: break-word;
        }

        .task-text.completed {
            text-decoration: line-through;
            color: #a0a0c0;
        }

        .task-actions {
            display: flex;
            gap: 10px;
        }

        .task-btn {
            background: transparent;
            border: none;
            color: #a0a0c0;
            cursor: pointer;
            font-size: 1.1rem;
            transition: all 0.2s ease;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .task-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .delete-btn:hover {
            background: rgba(255, 99, 99, 0.2);
            color: #ff6363;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #a0a0c0;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .input-section {
                flex-direction: column;
            }
            
            #add-btn {
                padding: 15px;
            }
            
            .filters {
                flex-wrap: wrap;
            }
            
            .filter-btn {
                flex: 0 0 calc(33.33% - 5px);
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>Neon Glow Todo</h1>
            <p class="subtitle">Organize your tasks with style</p>
        </header>
        
        <div class="input-section">
            <input type="text" id="task-input" placeholder="Add a new task...">
            <button id="add-btn"><i class="fas fa-plus"></i></button>
        </div>
        
        <div class="filters">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="active">Active</button>
            <button class="filter-btn" data-filter="completed">Completed</button>
        </div>
        
        <div class="stats">
            <span id="total-tasks">Total: 0 tasks</span>
            <span id="completed-tasks">Completed: 0</span>
        </div>
        
        <ul id="task-list">
            <!-- Tasks will be added here dynamically -->
        </ul>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const taskInput = document.getElementById('task-input');
            const addBtn = document.getElementById('add-btn');
            const taskList = document.getElementById('task-list');
            const filterBtns = document.querySelectorAll('.filter-btn');
            const totalTasksSpan = document.getElementById('total-tasks');
            const completedTasksSpan = document.getElementById('completed-tasks');
            
            let tasks = JSON.parse(localStorage.getItem('tasks')) || [];
            let currentFilter = 'all';
            
            // Initialize the app
            renderTasks();
            updateStats();
            
            // Add task
            addBtn.addEventListener('click', addTask);
            taskInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') addTask();
            });
            
            // Filter tasks
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    filterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentFilter = this.dataset.filter;
                    renderTasks();
                });
            });
            
            function addTask() {
                const text = taskInput.value.trim();
                if (text) {
                    const newTask = {
                        id: Date.now(),
                        text: text,
                        completed: false
                    };
                    
                    tasks.push(newTask);
                    saveTasks();
                    renderTasks();
                    updateStats();
                    taskInput.value = '';
                    taskInput.focus();
                }
            }
            
            function deleteTask(id) {
                tasks = tasks.filter(task => task.id !== id);
                saveTasks();
                renderTasks();
                updateStats();
            }
            
            function toggleTask(id) {
                tasks = tasks.map(task => {
                    if (task.id === id) {
                        return {...task, completed: !task.completed};
                    }
                    return task;
                });
                saveTasks();
                renderTasks();
                updateStats();
            }
            
            function saveTasks() {
                localStorage.setItem('tasks', JSON.stringify(tasks));
            }
            
            function renderTasks() {
                // Clear the task list
                taskList.innerHTML = '';
                
                // Filter tasks based on current filter
                let filteredTasks = tasks;
                if (currentFilter === 'active') {
                    filteredTasks = tasks.filter(task => !task.completed);
                } else if (currentFilter === 'completed') {
                    filteredTasks = tasks.filter(task => task.completed);
                }
                
                // Render tasks or empty state
                if (filteredTasks.length === 0) {
                    const emptyState = document.createElement('div');
                    emptyState.className = 'empty-state';
                    emptyState.innerHTML = `
                        <i class="fas fa-clipboard-list"></i>
                        <p>No tasks found. Add a new task to get started!</p>
                    `;
                    taskList.appendChild(emptyState);
                } else {
                    filteredTasks.forEach(task => {
                        const taskItem = document.createElement('li');
                        taskItem.className = 'task-item';
                        taskItem.innerHTML = `
                            <input type="checkbox" class="task-checkbox" ${task.completed ? 'checked' : ''}>
                            <span class="task-text ${task.completed ? 'completed' : ''}">${task.text}</span>
                            <div class="task-actions">
                                <button class="task-btn delete-btn"><i class="fas fa-trash-alt"></i></button>
                            </div>
                        `;
                        
                        const checkbox = taskItem.querySelector('.task-checkbox');
                        const deleteBtn = taskItem.querySelector('.delete-btn');
                        
                        checkbox.addEventListener('change', () => toggleTask(task.id));
                        deleteBtn.addEventListener('click', () => deleteTask(task.id));
                        
                        taskList.appendChild(taskItem);
                    });
                }
            }
            
            function updateStats() {
                const total = tasks.length;
                const completed = tasks.filter(task => task.completed).length;
                
                totalTasksSpan.textContent = `Total: ${total} ${total === 1 ? 'task' : 'tasks'}`;
                completedTasksSpan.textContent = `Completed: ${completed}`;
            }
        });
    </script>
</body>
</html>


    
              <script>
                              
              </script>
                        </body>
                        </html>
                    