import os
import django
from django.core.management.base import BaseCommand

# Setup Django first
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'todo.settings')
django.setup()

import click
from todo_cli.views import todo_app

class Command(BaseCommand):
    help = "Todo CLI using click"

    def handle(self, *args, **options):
        interactive_cli()

def interactive_cli():
    """Interactive CLI session like Gemini CLI"""
    app = todo_app()
    
    # Welcome message
    click.echo(click.style("🚀 Welcome to Todo CLI", fg='cyan', bold=True))
    click.echo(click.style("Type 'help' for available commands or 'exit' to quit\n", fg='yellow'))
    
    while True:
        try:
            # Get user input with a prompt
            user_input = click.prompt(
                click.style("todo", fg='blue', bold=True) + 
                click.style(" > ", fg='white'), 
                type=str
            ).strip()
            
            if not user_input:
                continue
                
            # Parse the command
            parts = user_input.split()
            command = parts[0].lower()
            args = parts[1:] if len(parts) > 1 else []
            
            # Handle commands
            if command == 'exit' or command == 'quit':
                click.echo(click.style("👋 Goodbye!", fg='cyan'))
                break
                
            elif command == 'help':
                show_help()
                
            elif command == 'add':
                if args:
                    description = ' '.join(args)
                    task = app.add_data(description)
                    click.echo(click.style(f'✓ Task added: {task.id} - {description}', fg='green'))
                else:
                    click.echo(click.style('Usage: add <description>', fg='red'))
                    
            elif command == 'list':
                completed_only = '--completed' in args
                tasks = app.list_tasks(completed_only=completed_only)
                if not tasks:
                    click.echo(click.style('No tasks found.', fg='yellow'))
                    
            elif command == 'complete':
                if args:
                    try:
                        task_id = int(args[0])
                        if app.Completed(task_id):
                            click.echo(click.style(f'✓ Task {task_id} marked as completed', fg='green'))
                        else:
                            click.echo(click.style(f'✗ Task {task_id} not found', fg='red'))
                    except ValueError:
                        click.echo(click.style('Task ID must be a number', fg='red'))
                else:
                    click.echo(click.style('Usage: complete <task_id>', fg='red'))
                    
            elif command == 'edit':
                if len(args) >= 2:
                    try:
                        task_id = int(args[0])
                        new_description = ' '.join(args[1:])
                        if app.edit_tasks(task_id, new_description):
                            click.echo(click.style(f'✓ Task {task_id} updated', fg='green'))
                        else:
                            click.echo(click.style(f'✗ Task {task_id} not found', fg='red'))
                    except ValueError:
                        click.echo(click.style('Task ID must be a number', fg='red'))
                else:
                    click.echo(click.style('Usage: edit <task_id> <new_description>', fg='red'))
                    
            elif command == 'delete':
                if args:
                    try:
                        task_id = int(args[0])
                        if app.delete_tasks(task_id):
                            click.echo(click.style(f'✓ Task {task_id} deleted', fg='green'))
                        else:
                            click.echo(click.style(f'✗ Task {task_id} not found', fg='red'))
                    except ValueError:
                        click.echo(click.style('Task ID must be a number', fg='red'))
                else:
                    click.echo(click.style('Usage: delete <task_id>', fg='red'))
                    
            elif command == 'clear':
                os.system('clear' if os.name == 'posix' else 'cls')
                
            else:
                click.echo(click.style(f"Unknown command: {command}. Type 'help' for available commands.", fg='red'))
                
        except KeyboardInterrupt:
            click.echo(click.style("\n👋 Goodbye!", fg='cyan'))
            break
        except EOFError:
            click.echo(click.style("\n👋 Goodbye!", fg='cyan'))
            break
        except Exception as e:
            click.echo(click.style(f"Error: {str(e)}", fg='red'))

def show_help():
    """Display help information"""
    help_text = """
📋 Available Commands:

  add <description>           Add a new task
  list [--completed]          List all tasks (or only completed ones)
  complete <task_id>          Mark a task as completed
  edit <task_id> <description> Edit a task description
  delete <task_id>            Delete a task
  clear                       Clear the screen
  help                        Show this help message
  exit/quit                   Exit the CLI

💡 Examples:
  add Buy groceries
  list
  list --completed
  complete 1
  edit 1 Buy organic groceries
  delete 1
    """
    click.echo(click.style(help_text, fg='cyan'))
