from django.db import models

# Create your models here.
class Task(models.Model):
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    completed = models.BooleanField(default=False)
    completed_at = models.DateTimeField(null=True, blank=True)
    def __str__(self):
        return f"Task {self.id}: {self.description}"



class Users(models.Model):
    name = models.CharField(max_length=100)
    age = models.IntegerField()

    def __str__(self):
        return f"The user name is {self.name}: and his age is {self.age}"