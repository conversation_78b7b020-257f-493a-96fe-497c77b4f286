from django.shortcuts import render
from django.utils import timezone
from todo_cli.models import Task
from typing import List, Dict
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from .serializer import UserSerializer
from .models import Users
 
# Create your views here.

class todo_app:
    def __init__(self):
        pass

    def add_data(self, description: str):
        task = Task.objects.create(description=description)
        return task


    def edit_tasks(self, task_id: int, new_description: str):
        try:
            task = Task.objects.get(id=task_id)
            task.description = new_description
            task.save()
            return True
        except Task.DoesNotExist:
            return False


    def delete_tasks(self, task_id: int):
        try:
            task = Task.objects.get(id=task_id)
            task.delete()
            return True
        except Task.DoesNotExist:
            return False

    def Completed(self, task_id: int):
        try:
            task = Task.objects.get(id=task_id)
            task.completed = True
            task.completed_at = timezone.now()
            task.save()
            return True
        except Task.DoesNotExist:
            return False

    def list_tasks(self, completed_only=False):
        if completed_only:
            tasks = Task.objects.filter(completed=True)
        else:
            tasks = Task.objects.all()

        for task in tasks:
            status = "✓" if task.completed else "○"
            completed = task.completed_at if task.completed else None
            print(f"[{status}] {task.id}: {task.description} :{task.created_at} : {completed}")
        
        return tasks


@api_view(["GET"])                
def user_api(request):
    users = Users.objects.all()
    sr = UserSerializer(users, many=True)
    return Response(sr.data)

@api_view(['POST'])
def make_users(request):
    
    user = UserSerializer(data=request.data)
    if user.is_valid():
        user.save()
        return Response(user.data, status=status.HTTP_201_CREATED)
    else:
        return Response(user.errors, status=status.HTTP_404_BAD_REQUEST)

@api_view(["GET","DELETE","PUT"])
def user_updates(request, pk):
    try:
        user = Users.objects.get(pk=pk)
    except Users.DoesNotExist:
        return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)

    if request.method == "GET":
        serializer = UserSerializer(user)
        return Response(serializer.data)

    elif request.method == "DELETE":
        user.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

    elif request.method == "PUT":
        serializer = UserSerializer(user, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
